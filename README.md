# MQ消息设计规范

## 消息规范性说明

- **消息中间件**: RocketMQ
- **序列化类型**: Protobuf

## 消息公共参数

### Header参数设置

发送消息时需要设置以下header参数：

```java
Message<byte[]> message = MessageBuilder
    .withPayload(order.toByteArray())
    .setHeader(RocketMQHeaders.KEYS, baseMsg.getKey())
    .setHeader("traceId", baseMsg.getTraceId()) // 新增自定义 traceId 头
    .build();
```

### 公共参数说明

| 参数 | 中文 | 类型 | 说明 |
|------|------|------|------|
| key | 消息的标识 | String | |
| traceId | 链路id | String | 用于链路追踪的id |
| sendTime | 发送时间 | Long | 时间戳 |
| retryTimes | 重试次数 | int | 用于设置重试次数 |

## Topic设计

### 订单类（ord）

| 序号 | Topic名称 | 生产者 | 消费者 | 用途说明 |
|------|-----------|--------|--------|----------|
| 1 | ord-submit.usr | 交易通道(api-ws-trade) | 订单预处理(eg-odpre) | |
| 2 | ord-submit.sys | 交易通道(api-ws-trade) | 订单预处理(eg-odpre) | |
| 3 | ord-submit-result | 订单预处理(eg-odpre) | 交易通道(api-ws-trade) | |
| 4 | ord-req.usr | 订单预处理(eg-odpre) | 订单清洗(eg-odf) | 用户下单请求 |
| 5 | ord-req.sys | 订单预处理(eg-odpre)/风控引擎(eg-re)/止盈止损(eg-gain) | 订单清洗(eg-odf) | 触发风控和止盈止损时候用于自动平仓时下单请求 |
| 6 | ord-deal.oz | 订单引擎(eg-oe) | 交易端口(oz)(bg-oz-trade) | 由下单引擎发送下单到桥的消息oz代表的是oneZero，如果有多个桥把oz替换为其他桥的简称 |
| 7 | ord-deal.de | 订单引擎(eg-oe) | 成交引擎(eg-de) | 由下单引擎发送下单到桥的消息oz代表的是oneZero，如果有多个桥把oz替换为其他桥的简称 |
| 8 | ord-exec | 交易端口(oz)(bg-oz-trade)/成交引擎(eg-de) | 结算引擎(eg-oe) | 交易网关执行报告回执的消息 |
| 9 | ord-cancle.oz | 订单引擎(eg-oe) | 交易端口(oz)(bg-oz-trade) | 订单取消 |

## 订单消息格式

### 1. 订单下单

- **MQ消息类型**: 发布订阅
- **Topic**: ord-deal.oz

#### 请求数据格式

| 序号 | 参数 | 中文名 | 类型 | 说明 |
|------|------|--------|------|------|
| 1 | accoutId | 账户id | String | 对应OZ的Account |
| 2 | userId | 用户id | String | |
| 3 | orderId | 订单ID | String | 对应OZ的ClOrdID |
| 4 | handIlnst | 订单执行指令 | Int | 对应OZ的HandIlnst |
| 5 | symbolCode | 合约编号 | String | 对应OZ的Symbol |
| 6 | side | 交易方向 | Int | 对应OZ的Side |
| 7 | orderQty | 交易量 | BigDecimal | 对应OZ的OrderQty |
| 8 | orderType | 订单类型 | String | 对应OZ的OrdType |
| 9 | price | 价格 | BigDecimal | 对应OZ的price |
| 10 | timeInForce | 订单执行策略 | Int | 对应OZ的TimeInForce |
| 11 | transactTime | 订单创建时间 | Long | 对应OZ的TransactTime，传时间戳 |
| 12 | quoteId | 行情id | String | 对应OZ的quoteId |

### 2. 订单执行报告

- **MQ消息类型**: 发布订阅
- **Topic**: ord-exec

#### 请求数据格式

| 序号 | 参数 | 中文名 | 类型 | 说明 |
|------|------|--------|------|------|
| 1 | orderId | 订单ID | String | 对应OZ的ClOrdID |
| 2 | fixOrderId | fix引擎产生的id | String | 对应OZ返回的OrderId |
| 3 | fixExecId | 桥返回的执行报告id | String | 对应OZ返回的ExecID |
| 4 | orderStatus | 订单执行状态 | String | 对应OZ返回的OrdStatus |
| 5 | execType | 执行的类型 | String | 对应OZ返回的ExecType |
| 6 | symbolCode | 合约编号 | String | 对应OZ的Symbol |
| 7 | side | 交易方向 | Int | 对应OZ的Side |
| 8 | orderQty | 交易量 | BigDecimal | 对应OZ的OrderQty |
| 9 | orderType | 订单类型 | String | 对应OZ的OrdType |
| 10 | price | 价格 | BigDecimal | 对应OZ的price |
| 11 | timeInForce | 订单执行策略 | Int | 对应OZ的TimeInForce |
| 12 | tradePrice | 成交价格 | BigDecimal | 对应OZ的LastPx |
| 13 | tradeOrderQty | 成交数量 | BigDecimal | 对应OZ的LastQty |
| 14 | orderAvgPrice | 成交均价 | BigDecimal | 对应OZ的AvgPx |
| 15 | untradeQty | 未成交数量 | BigDecimal | 对应OZ的LeavesQty |
| 16 | tradeTotalQty | 总成交数量 | BigDecimal | 对应OZ的CumQty |
| 17 | settlDate | 结算时间 | Long | 对应OZ的SettlDate |
| 18 | tradeDate | 交易时间 | Long | 对应OZ的TradeDate |
| 19 | orderRejectReason | 订单拒绝原因 | String | 对应OZ的OrdRejReason |
| 20 | remark | 备注 | String | 对应OZ的Text |
| 21 | cancleRejectReason | 取消订单拒绝原因 | String | 对应OZ的OrdRejReason |
| 22 | cancleRejectResponse | 取消订单拒绝的响应内容 | String | 对应OZ的CxlRejResponseTo |
| 23 | cancleRejectTransactTime | 订单最后一次处理时间 | Long | 对应OZ的TransactTime，传时间戳 |
| 24 | cancleOrderId | 取消订单的id | String | 对应取消拒绝时候的OZ的ClOrdID |

### 3. 订单取消

- **MQ消息类型**: 发布订阅
- **Topic**: ord-cancle.oz

#### 请求数据格式

| 序号 | 参数 | 中文名 | 类型 | 说明 |
|------|------|--------|------|------|
| 1 | orderId | 原始订单ID | String | 对应OZ的OrigClOrdID |
| 2 | cancleOrderId | 取消订单的id | String | 对应OZ的ClOrdID |
| 3 | side | 交易方向 | Int | 对应OZ的Side |
| 4 | transactTime | 订单创建时间 | Long | 对应OZ的TransactTime，传时间戳 |

## 注意事项

1. 所有消息都使用Protobuf进行序列化
2. 时间字段统一使用时间戳格式（Long类型）
3. 价格和数量字段使用BigDecimal类型确保精度
4. 每个消息都需要设置公共的header参数，包括key和traceId用于消息追踪
5. Topic命名规范：业务类型-操作类型.目标系统简称
