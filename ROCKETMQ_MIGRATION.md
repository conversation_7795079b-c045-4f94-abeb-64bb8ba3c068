# Kafka到RocketMQ迁移指南

本文档描述了项目从Kafka迁移到RocketMQ的详细信息。

## 迁移概述

项目已成功从Apache Kafka迁移到Apache RocketMQ，同时保持了protobuf序列化功能。主要变更包括：

- 移除了所有Kafka相关依赖和配置
- 添加了RocketMQ Spring Boot Starter
- 重新实现了生产者和消费者
- 保持了protobuf序列化机制
- 支持有序消息和延迟消息

## 主要组件

### 1. 配置类

- `RocketMQConfig` - RocketMQ通用配置
- `RocketMQProducerConfig` - 生产者配置
- `RocketMQConsumerConfig` - 消费者配置

### 2. 核心服务

- `RocketMQMarketDataProducer` - 市场数据生产者
- `RocketMQMarketDataConsumer` - 市场数据消费者
- `MarketDataService` - 整合服务（已更新使用RocketMQ）

### 3. 工具类

- `ProtobufSerializer` - Protobuf序列化工具（已更新注释）

## 配置说明

### application.properties配置

```properties
# RocketMQ配置
rocketmq.name-server=localhost:9876

# RocketMQ生产者配置
rocketmq.producer.group=bg-oz-quote-producer-group
rocketmq.producer.send-message-timeout=3000
rocketmq.producer.retry-times-when-send-failed=2
rocketmq.producer.retry-times-when-send-async-failed=2
rocketmq.producer.max-message-size=4194304
rocketmq.producer.compress-message-body-threshold=4096
rocketmq.producer.timeout=3000

# RocketMQ消费者配置
rocketmq.consumer.group=bg-oz-quote-consumer-group
rocketmq.consumer.consume-thread-min=20
rocketmq.consumer.consume-thread-max=64
rocketmq.consumer.consume-message-batch-max-size=1
rocketmq.consumer.pull-batch-size=32
rocketmq.consumer.pull-interval=0
rocketmq.consumer.consume-timeout=15
rocketmq.consumer.max-reconsume-times=16

# RocketMQ Topic配置
rocketmq.topic.market-data=market-data-topic
```

## 功能特性

### 1. 有序消息

RocketMQ生产者支持有序消息发送，确保同一symbol的消息按顺序处理：

```java
// 使用symbol作为分区key，确保有序
SendResult result = rocketMQTemplate.syncSendOrderly(
    marketDataTopic + ":" + tag, 
    message, 
    messageKey, // symbol作为分区key
    timeout
);
```

### 2. 消息标签

支持使用symbol作为消息标签，便于消费者过滤：

```java
// 设置消息标签
Message<byte[]> message = MessageBuilder
    .withPayload(serializedData)
    .setHeader("KEYS", messageKey)
    .setHeader("TAGS", tag) // symbol作为tag
    .build();
```

### 3. 延迟消息

支持发送延迟消息：

```java
public SendResult sendDelayedMarketData(MarketDataMsg.MarketData marketData, int delayLevel) {
    // delayLevel: 1-18，对应不同的延迟时间
    return rocketMQTemplate.syncSend(topic, message, timeout, delayLevel);
}
```

### 4. 异步发送

支持异步消息发送以提高性能：

```java
public void sendMarketDataAsync(MarketDataMsg.MarketData marketData) {
    rocketMQTemplate.asyncSendOrderly(topic, message, messageKey, callback, timeout);
}
```

## 消费者特性

### 1. 有序消费

消费者配置为有序消费模式：

```java
@RocketMQMessageListener(
    topic = "${rocketmq.topic.market-data}",
    consumerGroup = "${rocketmq.consumer.group}",
    messageModel = MessageModel.CLUSTERING,
    consumeMode = ConsumeMode.ORDERLY, // 有序消费
    maxReconsumeTimes = 3
)
```

### 2. 集群消费

使用集群消费模式，确保消息负载均衡。

### 3. 重试机制

支持消息重试，最大重试次数为3次。

## 部署要求

### 1. RocketMQ服务器

需要部署RocketMQ NameServer和Broker：

```bash
# 启动NameServer
nohup sh mqnamesrv &

# 启动Broker
nohup sh mqbroker -n localhost:9876 &
```

### 2. Topic创建

需要预先创建Topic：

```bash
sh mqadmin updateTopic -n localhost:9876 -t market-data-topic -c DefaultCluster
```

## 测试

### 1. 单元测试

项目包含完整的单元测试：

- `RocketMQMarketDataProducerTest` - 生产者测试
- `RocketMQMarketDataConsumerTest` - 消费者测试
- `MarketDataServiceTest` - 服务层测试

运行测试：

```bash
mvn test
```

### 2. 集成测试

项目包含集成测试示例：

- `RocketMQProtobufExample` - 基本功能演示
- `RocketMQMessageTest` - 消息发送测试

## 性能优化

### 1. 生产者优化

- 启用消息压缩（阈值4KB）
- 批量发送配置
- 连接池复用

### 2. 消费者优化

- 多线程消费（20-64线程）
- 批量拉取消息
- 合理的超时配置

## 监控和运维

### 1. 日志监控

所有关键操作都有详细日志记录，包括：

- 消息发送成功/失败
- 消息消费状态
- 性能指标

### 2. 指标监控

建议监控以下指标：

- 消息发送TPS
- 消息消费TPS
- 消息堆积量
- 错误率

## 故障排查

### 1. 常见问题

1. **连接失败**：检查NameServer地址配置
2. **消息发送失败**：检查Topic是否存在
3. **消费者不工作**：检查消费者组配置

### 2. 日志分析

查看应用日志中的RocketMQ相关信息：

```bash
grep -i "rocketmq\|market.*data" logs/application.log
```

## 迁移检查清单

- [x] 移除Kafka依赖
- [x] 添加RocketMQ依赖
- [x] 更新配置文件
- [x] 实现RocketMQ生产者
- [x] 实现RocketMQ消费者
- [x] 更新服务类
- [x] 移除Kafka相关代码
- [x] 更新测试代码
- [x] 创建单元测试
- [x] 验证功能正确性

## 注意事项

1. **消息格式兼容性**：protobuf消息格式保持不变
2. **有序性保证**：RocketMQ提供更强的有序性保证
3. **性能差异**：RocketMQ在某些场景下性能可能与Kafka不同
4. **运维工具**：需要熟悉RocketMQ的运维工具和命令

## 后续优化建议

1. 根据实际业务量调整线程池大小
2. 监控消息堆积情况，及时调整消费能力
3. 考虑使用RocketMQ的事务消息功能
4. 评估是否需要消息过滤功能
