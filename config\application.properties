spring.application.name=bg-oz-quote

logging.config=classpath:logback-spring.xml
logging.level.com.fudianyun=DEBUG
logging.level.org.springframework=INFO
logging.level.org.apache=WARN
logging.file.path=logs
logging.charset.console=UTF-8
logging.charset.file=UTF-8

quickfix.client.config=classpath:quickfix-client.cfg
quickfix.log.enabled=true
quickfix.log.path=logs/quickfix

quickfix.onezero.auto-start=true
quickfix.onezero.password=NwzhXY9vkn
quickfix.onezero.subscribe.symbol=EURUSD,GBPUSD

# RocketMQ配置
rocketmq.name-server=192.168.1.227:9876

# RocketMQ生产者配置
rocketmq.producer.group=bg-oz-quote-producer-group
rocketmq.producer.send-message-timeout=3000
rocketmq.producer.retry-times-when-send-failed=2
rocketmq.producer.retry-times-when-send-async-failed=2
rocketmq.producer.max-message-size=4194304
rocketmq.producer.compress-message-body-threshold=4096
rocketmq.producer.timeout=3000

# RocketMQ消费者配置
rocketmq.consumer.group=bg-oz-quote-consumer-group
rocketmq.consumer.consume-thread-min=20
rocketmq.consumer.consume-thread-max=64
rocketmq.consumer.consume-message-batch-max-size=1
rocketmq.consumer.pull-batch-size=32
rocketmq.consumer.pull-interval=0
rocketmq.consumer.consume-timeout=15
rocketmq.consumer.max-reconsume-times=16

# RocketMQ Topic配置
rocketmq.topic.market-data=market-data-topic

