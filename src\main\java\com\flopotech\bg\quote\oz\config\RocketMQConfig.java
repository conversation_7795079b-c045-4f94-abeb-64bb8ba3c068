package com.flopotech.bg.quote.oz.config;

import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ通用配置类
 * 负责RocketMQ的基础配置和Topic管理
 */
@Configuration
public class RocketMQConfig {

    @Value("${rocketmq.name-server}")
    private String nameServer;

    @Value("${rocketmq.topic.market-data}")
    private String marketDataTopic;

    @Value("${rocketmq.producer.group}")
    private String producerGroup;

    /**
     * 获取市场数据Topic名称
     */
    public String getMarketDataTopic() {
        return marketDataTopic;
    }

    /**
     * 获取生产者组名
     */
    public String getProducerGroup() {
        return producerGroup;
    }

    /**
     * 获取NameServer地址
     */
    public String getNameServer() {
        return nameServer;
    }
}
