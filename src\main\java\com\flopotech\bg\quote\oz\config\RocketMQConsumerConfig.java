package com.flopotech.bg.quote.oz.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ Consumer配置类
 * 专门负责消费者相关配置
 */
@Configuration
public class RocketMQConsumerConfig {

    @Value("${rocketmq.name-server}")
    private String nameServer;

    @Value("${rocketmq.consumer.group}")
    private String consumerGroup;

    @Value("${rocketmq.consumer.consume-thread-min:20}")
    private int consumeThreadMin;

    @Value("${rocketmq.consumer.consume-thread-max:64}")
    private int consumeThreadMax;

    @Value("${rocketmq.consumer.consume-message-batch-max-size:1}")
    private int consumeMessageBatchMaxSize;

    @Value("${rocketmq.consumer.pull-batch-size:32}")
    private int pullBatchSize;

    @Value("${rocketmq.consumer.pull-interval:0}")
    private long pullInterval;

    @Value("${rocketmq.consumer.consume-timeout:15}")
    private long consumeTimeout;

    @Value("${rocketmq.consumer.max-reconsume-times:16}")
    private int maxReconsumeTimes;

    /**
     * 获取NameServer地址
     */
    public String getNameServer() {
        return nameServer;
    }

    /**
     * 获取消费者组名
     */
    public String getConsumerGroup() {
        return consumerGroup;
    }

    /**
     * 获取最小消费线程数
     */
    public int getConsumeThreadMin() {
        return consumeThreadMin;
    }

    /**
     * 获取最大消费线程数
     */
    public int getConsumeThreadMax() {
        return consumeThreadMax;
    }

    /**
     * 获取批量消费最大消息数
     */
    public int getConsumeMessageBatchMaxSize() {
        return consumeMessageBatchMaxSize;
    }

    /**
     * 获取拉取批次大小
     */
    public int getPullBatchSize() {
        return pullBatchSize;
    }

    /**
     * 获取拉取间隔
     */
    public long getPullInterval() {
        return pullInterval;
    }

    /**
     * 获取消费超时时间
     */
    public long getConsumeTimeout() {
        return consumeTimeout;
    }

    /**
     * 获取最大重试次数
     */
    public int getMaxReconsumeTimes() {
        return maxReconsumeTimes;
    }
}
