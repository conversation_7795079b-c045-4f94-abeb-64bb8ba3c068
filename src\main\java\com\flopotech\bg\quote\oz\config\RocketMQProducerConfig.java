package com.flopotech.bg.quote.oz.config;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RocketMQ Producer配置类
 * 专门负责生产者相关配置
 */
@Configuration
public class RocketMQProducerConfig {

    @Value("${rocketmq.name-server}")
    private String nameServer;

    @Value("${rocketmq.producer.group}")
    private String producerGroup;

    @Value("${rocketmq.producer.send-message-timeout:3000}")
    private int sendMessageTimeout;

    @Value("${rocketmq.producer.retry-times-when-send-failed:2}")
    private int retryTimesWhenSendFailed;

    @Value("${rocketmq.producer.retry-times-when-send-async-failed:2}")
    private int retryTimesWhenSendAsyncFailed;

    @Value("${rocketmq.producer.max-message-size:4194304}")
    private int maxMessageSize;

    @Value("${rocketmq.producer.compress-message-body-threshold:4096}")
    private int compressMessageBodyThreshold;

    /**
     * 创建RocketMQ生产者
     */
    @Bean
    public DefaultMQProducer defaultMQProducer() {
        DefaultMQProducer producer = new DefaultMQProducer();
        producer.setNamesrvAddr(nameServer);
        producer.setProducerGroup(producerGroup);
        producer.setSendMsgTimeout(sendMessageTimeout);
        producer.setRetryTimesWhenSendFailed(retryTimesWhenSendFailed);
        producer.setRetryTimesWhenSendAsyncFailed(retryTimesWhenSendAsyncFailed);
        producer.setMaxMessageSize(maxMessageSize);
        producer.setCompressMsgBodyOverHowmuch(compressMessageBodyThreshold);
        
        // 启用VIP通道（可选）
        producer.setVipChannelEnabled(false);
        
        return producer;
    }

    /**
     * 创建RocketMQ模板
     */
    @Bean
    public RocketMQTemplate rocketMQTemplate() {
        RocketMQTemplate template = new RocketMQTemplate();
        template.setProducer(defaultMQProducer());
        return template;
    }
}
