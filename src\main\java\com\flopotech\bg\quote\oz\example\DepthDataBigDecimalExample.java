package com.flopotech.bg.quote.oz.example;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.BigDecimalUtils;
import com.flopotech.bg.quote.oz.util.MarketDataBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 深度数据BigDecimal处理示例
 * 展示如何在实际应用中使用BigDecimal处理深度数据，确保精度不丢失
 */
@Component
public class DepthDataBigDecimalExample {

    private static final Logger logger = LoggerFactory.getLogger(DepthDataBigDecimalExample.class);

    private final MarketDataBuilder marketDataBuilder;

    public DepthDataBigDecimalExample(MarketDataBuilder marketDataBuilder) {
        this.marketDataBuilder = marketDataBuilder;
    }

    /**
     * 演示创建和处理高精度深度数据
     */
    public void demonstrateHighPrecisionDepthData() {
        logger.info("=== 深度数据BigDecimal精度处理示例 ===");

        // 1. 创建高精度的深度数据
        List<MarketDataMsg.DepthData> depthDataList = createHighPrecisionDepthData();

        // 2. 创建包含深度数据的市场数据
        BigDecimal bid = new BigDecimal("1.085012345678901234567890");
        BigDecimal offer = new BigDecimal("1.085212345678901234567890");
        
        MarketDataMsg.MarketData marketData = marketDataBuilder.createMarketDataWithDepth(
                System.currentTimeMillis(), "EURUSD", bid, offer, depthDataList);

        // 3. 处理和分析深度数据
        processDepthDataWithBigDecimal(marketData);

        // 4. 演示精度计算
        demonstratePrecisionCalculations(marketData);
    }

    /**
     * 创建高精度的深度数据
     */
    private List<MarketDataMsg.DepthData> createHighPrecisionDepthData() {
        logger.info("创建高精度深度数据...");

        // 买方深度数据 - 使用高精度BigDecimal
        MarketDataMsg.DepthData bid1 = marketDataBuilder.createTradeableDepthData(
                0, 
                new BigDecimal("1.085012345678901234567890"), 
                new BigDecimal("1000000.123456789012345"), 
                "LP1", "bid-001");

        MarketDataMsg.DepthData bid2 = marketDataBuilder.createTradeableDepthData(
                0, 
                new BigDecimal("1.084912345678901234567890"), 
                new BigDecimal("2000000.987654321098765"), 
                "LP2", "bid-002");

        // 卖方深度数据 - 使用高精度BigDecimal
        MarketDataMsg.DepthData offer1 = marketDataBuilder.createTradeableDepthData(
                1, 
                new BigDecimal("1.085212345678901234567890"), 
                new BigDecimal("1500000.555666777888999"), 
                "LP1", "offer-001");

        MarketDataMsg.DepthData offer2 = marketDataBuilder.createIndicativeDepthData(
                1, 
                new BigDecimal("1.085312345678901234567890"), 
                new BigDecimal("3000000.111222333444555"), 
                "LP3", "offer-002");

        return Arrays.asList(bid1, bid2, offer1, offer2);
    }

    /**
     * 使用BigDecimal处理深度数据
     */
    private void processDepthDataWithBigDecimal(MarketDataMsg.MarketData marketData) {
        logger.info("处理深度数据 - Symbol: {}, 深度条目数: {}", 
                marketData.getSymbol(), marketData.getDataCount());

        // 提取主要价格信息
        BigDecimal bid = BigDecimalUtils.getBidFromMarketData(marketData);
        BigDecimal offer = BigDecimalUtils.getOfferFromMarketData(marketData);
        BigDecimal mid = BigDecimalUtils.getMidFromMarketData(marketData);

        logger.info("主要价格 - Bid: {}, Offer: {}, Mid: {}", bid, offer, mid);

        // 处理每个深度条目
        BigDecimal totalBidVolume = BigDecimal.ZERO;
        BigDecimal totalOfferVolume = BigDecimal.ZERO;
        BigDecimal totalBidValue = BigDecimal.ZERO;
        BigDecimal totalOfferValue = BigDecimal.ZERO;

        for (MarketDataMsg.DepthData depthData : marketData.getDataList()) {
            BigDecimal price = BigDecimalUtils.getPriceFromDepthData(depthData);
            BigDecimal size = BigDecimalUtils.getSizeFromDepthData(depthData);
            BigDecimal value = BigDecimalUtils.safeMultiply(price, size);

            String typeStr = (depthData.getTp() == 0) ? "BID" : "OFFER";
            
            logger.info("深度数据 - Type: {}, Price: {}, Size: {}, Value: {}, Condition: {}, Origin: {}", 
                    typeStr, price, size, value, depthData.getCondition(), depthData.getOri());

            // 累计统计
            if (depthData.getTp() == 0) { // BID
                totalBidVolume = BigDecimalUtils.safeAdd(totalBidVolume, size);
                totalBidValue = BigDecimalUtils.safeAdd(totalBidValue, value);
            } else { // OFFER
                totalOfferVolume = BigDecimalUtils.safeAdd(totalOfferVolume, size);
                totalOfferValue = BigDecimalUtils.safeAdd(totalOfferValue, value);
            }
        }

        logger.info("统计结果 - 总BID量: {}, 总BID价值: {}, 总OFFER量: {}, 总OFFER价值: {}", 
                totalBidVolume, totalBidValue, totalOfferVolume, totalOfferValue);
    }

    /**
     * 演示精度计算
     */
    private void demonstratePrecisionCalculations(MarketDataMsg.MarketData marketData) {
        logger.info("=== 精度计算演示 ===");

        BigDecimal bid = BigDecimalUtils.getBidFromMarketData(marketData);
        BigDecimal offer = BigDecimalUtils.getOfferFromMarketData(marketData);

        // 计算价差
        BigDecimal spread = BigDecimalUtils.safeSubtract(offer, bid);
        logger.info("价差 (Offer - Bid): {}", spread);

        // 计算价差百分比
        BigDecimal spreadPercentage = BigDecimalUtils.safeDivide(
                BigDecimalUtils.safeMultiply(spread, new BigDecimal("100")), bid);
        logger.info("价差百分比: {}%", BigDecimalUtils.formatToString(spreadPercentage, 6));

        // 计算加权平均价格
        BigDecimal weightedAvgPrice = calculateWeightedAveragePrice(marketData);
        logger.info("加权平均价格: {}", weightedAvgPrice);

        // 验证精度保持
        demonstratePrecisionPreservation();
    }

    /**
     * 计算加权平均价格
     */
    private BigDecimal calculateWeightedAveragePrice(MarketDataMsg.MarketData marketData) {
        BigDecimal totalValue = BigDecimal.ZERO;
        BigDecimal totalVolume = BigDecimal.ZERO;

        for (MarketDataMsg.DepthData depthData : marketData.getDataList()) {
            BigDecimal price = BigDecimalUtils.getPriceFromDepthData(depthData);
            BigDecimal size = BigDecimalUtils.getSizeFromDepthData(depthData);
            BigDecimal value = BigDecimalUtils.safeMultiply(price, size);

            totalValue = BigDecimalUtils.safeAdd(totalValue, value);
            totalVolume = BigDecimalUtils.safeAdd(totalVolume, size);
        }

        return BigDecimalUtils.safeDivide(totalValue, totalVolume);
    }

    /**
     * 演示精度保持
     */
    private void demonstratePrecisionPreservation() {
        logger.info("=== 精度保持验证 ===");

        // 使用double会丢失精度的例子
        double doublePrice = 1.085012345678901234567890;
        BigDecimal fromDouble = BigDecimalUtils.fromDouble(doublePrice);
        logger.info("从double转换: {} -> {}", doublePrice, fromDouble);

        // 使用BigDecimal保持精度的例子
        String precisePrice = "1.085012345678901234567890";
        BigDecimal fromString = BigDecimalUtils.fromString(precisePrice);
        logger.info("从字符串转换: {} -> {}", precisePrice, fromString);

        // 往返转换验证
        String backToString = BigDecimalUtils.toString(fromString);
        BigDecimal roundTrip = BigDecimalUtils.fromString(backToString);
        logger.info("往返转换验证: {} -> {} -> {}", precisePrice, backToString, roundTrip);
        logger.info("精度是否保持: {}", fromString.equals(roundTrip));

        // 计算精度验证
        BigDecimal a = new BigDecimal("1.085012345678901234567890");
        BigDecimal b = new BigDecimal("2.170024691357802469135780");
        BigDecimal sum = BigDecimalUtils.safeAdd(a, b);
        BigDecimal expectedSum = new BigDecimal("3.255037037036703703703670");
        
        logger.info("高精度加法: {} + {} = {}", a, b, sum);
        logger.info("计算结果正确: {}", sum.compareTo(expectedSum) == 0);
    }

    /**
     * 运行完整示例
     */
    public void runExample() {
        try {
            demonstrateHighPrecisionDepthData();
            logger.info("=== 深度数据BigDecimal处理示例完成 ===");
        } catch (Exception e) {
            logger.error("运行示例时发生错误: {}", e.getMessage(), e);
        }
    }
}
