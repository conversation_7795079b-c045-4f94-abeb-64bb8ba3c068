package com.flopotech.bg.quote.oz.example;

import com.flopotech.bg.quote.oz.service.MarketDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * RocketMQ + Protobuf 使用示例
 * 演示如何发送和接收市场数据
 */
@Component
@Order(1) // 首先运行基本示例
public class RocketMQProtobufExample implements CommandLineRunner {

        private static final Logger logger = LoggerFactory.getLogger(RocketMQProtobufExample.class);

        @Autowired
        private MarketDataService marketDataService;

        @Override
        public void run(String... args) throws Exception {
                // 注意：这个示例只在启动时运行一次
                // 在生产环境中，您应该根据实际的市场数据源来触发这些方法

                logger.info("=== RocketMQ + Protobuf 示例开始 ===");

                // 示例1：发送简单的市场数据
                sendSimpleMarketData();

                // 示例2：发送包含深度数据的市场数据
                sendMarketDataWithDepth();

                // 示例3：演示有序消息发送
                sendOrderedMarketData();

                logger.info("=== RocketMQ + Protobuf 示例结束 ===");
        }

        /**
         * 发送简单的市场数据示例
         */
        private void sendSimpleMarketData() {
                logger.info("--- 发送简单市场数据示例 ---");

                // 模拟EURUSD市场数据
                marketDataService.processAndSendMarketData(
                                "EURUSD",
                                1.0850, // bid
                                1.0852, // offer
                                System.currentTimeMillis());

                // 模拟GBPUSD市场数据
                marketDataService.processAndSendMarketData(
                                "GBPUSD",
                                1.2650, // bid
                                1.2653, // offer
                                System.currentTimeMillis());
        }

        /**
         * 发送包含深度数据的市场数据示例
         */
        private void sendMarketDataWithDepth() {
                logger.info("--- 发送深度市场数据示例 ---");

                // 创建买方深度数据
                List<MarketDataService.DepthInfo> bidDepths = Arrays.asList(
                                new MarketDataService.DepthInfo(1.0850, 1000000, "A", "LP1", "bid-001"),
                                new MarketDataService.DepthInfo(1.0849, 2000000, "A", "LP2", "bid-002"),
                                new MarketDataService.DepthInfo(1.0848, 1500000, "I", "LP3", "bid-003"));

                // 创建卖方深度数据
                List<MarketDataService.DepthInfo> offerDepths = Arrays.asList(
                                new MarketDataService.DepthInfo(1.0852, 1000000, "A", "LP1", "offer-001"),
                                new MarketDataService.DepthInfo(1.0853, 2000000, "A", "LP2", "offer-002"),
                                new MarketDataService.DepthInfo(1.0854, 1500000, "I", "LP3", "offer-003"));

                // 发送包含深度数据的市场数据
                marketDataService.processAndSendMarketDataWithDepth(
                                "EURUSD",
                                1.0850, // 最优bid
                                1.0852, // 最优offer
                                System.currentTimeMillis(),
                                bidDepths,
                                offerDepths);
        }

        /**
         * 发送有序市场数据示例
         * 演示RocketMQ的有序消息功能
         */
        private void sendOrderedMarketData() {
                logger.info("--- 发送有序市场数据示例 ---");

                // 为同一个symbol发送多条消息，验证有序性
                String symbol = "USDJPY";
                
                for (int i = 1; i <= 3; i++) {
                        double bid = 150.00 + i * 0.01;
                        double offer = bid + 0.02;
                        
                        marketDataService.processAndSendMarketData(
                                        symbol,
                                        bid,
                                        offer,
                                        System.currentTimeMillis());
                        
                        logger.info("发送有序消息 {}/3: Symbol={}, Bid={}, Offer={}", i, symbol, bid, offer);
                        
                        try {
                                Thread.sleep(100); // 短暂延迟
                        } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                                break;
                        }
                }
        }
}
