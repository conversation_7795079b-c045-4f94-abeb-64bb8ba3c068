package com.flopotech.bg.quote.oz.message;

import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;

import java.util.UUID;

/**
 * RocketMQ基础消息类
 * 包含消息发送时的基础header字段
 */
public class BaseMessage {

    /**
     * 消息的标识
     */
    private String key;

    /**
     * 链路追踪ID
     */
    private String traceId;

    /**
     * 发送时间戳
     */
    private Long sendTime;

    /**
     * 重试次数
     */
    private Integer retryTimes;

    /**
     * 默认构造函数
     */
    public BaseMessage() {
        this.key = UUID.randomUUID().toString();
        this.traceId = UUID.randomUUID().toString();
        this.sendTime = System.currentTimeMillis();
        this.retryTimes = 0;
    }

    /**
     * 带参数的构造函数
     *
     * @param key 消息标识
     */
    public BaseMessage(String key) {
        this.key = key;
        this.traceId = UUID.randomUUID().toString();
        this.sendTime = System.currentTimeMillis();
        this.retryTimes = 0;
    }

    /**
     * 完整参数构造函数
     *
     * @param key        消息标识
     * @param traceId    链路追踪ID
     * @param sendTime   发送时间
     * @param retryTimes 重试次数
     */
    public BaseMessage(String key, String traceId, Long sendTime, Integer retryTimes) {
        this.key = key;
        this.traceId = traceId;
        this.sendTime = sendTime;
        this.retryTimes = retryTimes;
    }

    /**
     * 构建Spring Message对象，将基础字段设置为header
     *
     * @param payload 消息体
     * @param <T>     消息体类型
     * @return Spring Message对象
     */
    public <T> Message<T> buildMessage(T payload) {
        return MessageBuilder
                .withPayload(payload)
                .setHeader("key", this.key)
                .setHeader("traceId", this.traceId)
                .setHeader("sendTime", this.sendTime)
                .setHeader("retryTimes", this.retryTimes)
                .build();
    }

    /**
     * 构建Spring Message对象，支持自定义header
     *
     * @param payload 消息体
     * @param tag     消息标签
     * @param <T>     消息体类型
     * @return Spring Message对象
     */
    public <T> Message<T> buildMessage(T payload, String tag) {
        return MessageBuilder
                .withPayload(payload)
                .setHeader("KEYS", this.key)
                .setHeader("TAGS", tag)
                .setHeader("traceId", this.traceId)
                .setHeader("sendTime", this.sendTime)
                .setHeader("retryTimes", this.retryTimes)
                .build();
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryTimes() {
        this.retryTimes++;
    }

    /**
     * 重置发送时间为当前时间
     */
    public void resetSendTime() {
        this.sendTime = System.currentTimeMillis();
    }

    /**
     * 创建重试消息
     *
     * @return 新的BaseMessage实例，重试次数+1，发送时间更新
     */
    public BaseMessage createRetryMessage() {
        return new BaseMessage(
                this.key,
                this.traceId,
                System.currentTimeMillis(),
                this.retryTimes + 1
        );
    }

    // Getter和Setter方法

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public Long getSendTime() {
        return sendTime;
    }

    public void setSendTime(Long sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    @Override
    public String toString() {
        return "BaseMessage{" +
                "key='" + key + '\'' +
                ", traceId='" + traceId + '\'' +
                ", sendTime=" + sendTime +
                ", retryTimes=" + retryTimes +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        BaseMessage that = (BaseMessage) o;

        if (!key.equals(that.key)) return false;
        return traceId.equals(that.traceId);
    }

    @Override
    public int hashCode() {
        int result = key.hashCode();
        result = 31 * result + traceId.hashCode();
        return result;
    }
}
