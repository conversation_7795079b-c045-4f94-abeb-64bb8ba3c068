package com.flopotech.bg.quote.oz.message;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import org.springframework.messaging.Message;

/**
 * 市场数据消息类
 * 专门用于市场数据的RocketMQ消息封装
 */
public class MarketDataMessage extends BaseMessage {

    /**
     * 市场数据内容
     */
    private MarketDataMsg.MarketData marketData;

    /**
     * 消息标签（通常使用symbol）
     */
    private String tag;

    /**
     * 默认构造函数
     */
    public MarketDataMessage() {
        super();
    }

    /**
     * 带市场数据的构造函数
     *
     * @param marketData 市场数据
     */
    public MarketDataMessage(MarketDataMsg.MarketData marketData) {
        super(marketData.getSymbol()); // 使用symbol作为key
        this.marketData = marketData;
        this.tag = marketData.getSymbol(); // 使用symbol作为tag
    }

    /**
     * 完整参数构造函数
     *
     * @param marketData 市场数据
     * @param key        消息标识
     * @param traceId    链路追踪ID
     */
    public MarketDataMessage(MarketDataMsg.MarketData marketData, String key, String traceId) {
        super(key, traceId, System.currentTimeMillis(), 0);
        this.marketData = marketData;
        this.tag = marketData.getSymbol();
    }

    /**
     * 构建用于RocketMQ发送的Message对象
     *
     * @param serializedData 序列化后的数据
     * @return Spring Message对象
     */
    public Message<byte[]> buildRocketMQMessage(byte[] serializedData) {
        return buildMessage(serializedData, this.tag);
    }

    /**
     * 创建重试消息
     *
     * @return 新的MarketDataMessage实例
     */
    @Override
    public MarketDataMessage createRetryMessage() {
        MarketDataMessage retryMessage = new MarketDataMessage(
                this.marketData,
                this.getKey(),
                this.getTraceId()
        );
        retryMessage.setRetryTimes(this.getRetryTimes() + 1);
        retryMessage.setSendTime(System.currentTimeMillis());
        retryMessage.setTag(this.tag);
        return retryMessage;
    }

    /**
     * 获取消息的业务标识（symbol）
     *
     * @return symbol
     */
    public String getSymbol() {
        return marketData != null ? marketData.getSymbol() : null;
    }

    /**
     * 获取消息的时间戳
     *
     * @return 市场数据时间戳
     */
    public Long getMarketDataTimestamp() {
        return marketData != null ? marketData.getTimestamp() : null;
    }

    /**
     * 检查是否为有效的市场数据消息
     *
     * @return 是否有效
     */
    public boolean isValid() {
        return marketData != null 
                && marketData.getSymbol() != null 
                && !marketData.getSymbol().trim().isEmpty()
                && marketData.getTimestamp() > 0;
    }

    // Getter和Setter方法

    public MarketDataMsg.MarketData getMarketData() {
        return marketData;
    }

    public void setMarketData(MarketDataMsg.MarketData marketData) {
        this.marketData = marketData;
        if (marketData != null) {
            this.tag = marketData.getSymbol();
        }
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    @Override
    public String toString() {
        return "MarketDataMessage{" +
                "symbol='" + getSymbol() + '\'' +
                ", tag='" + tag + '\'' +
                ", key='" + getKey() + '\'' +
                ", traceId='" + getTraceId() + '\'' +
                ", sendTime=" + getSendTime() +
                ", retryTimes=" + getRetryTimes() +
                ", marketDataTimestamp=" + getMarketDataTimestamp() +
                '}';
    }
}
