package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.BigDecimalUtils;
import com.flopotech.bg.quote.oz.util.ProtobufSerializer;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.context.annotation.Profile;

import java.math.BigDecimal;

/**
 * 市场数据RocketMQ消费者服务
 * 负责从RocketMQ Topic消费市场数据
 */
@Service
@Profile("!test")
@RocketMQMessageListener(topic = "${rocketmq.topic.market-data}", consumerGroup = "${rocketmq.consumer.group}", messageModel = MessageModel.CLUSTERING, consumeMode = ConsumeMode.ORDERLY, maxReconsumeTimes = 3)
public class RocketMQMarketDataConsumer implements RocketMQListener<byte[]> {

    private static final Logger logger = LoggerFactory.getLogger(RocketMQMarketDataConsumer.class);

    @Autowired
    private ProtobufSerializer protobufSerializer;

    /**
     * 消费市场数据消息
     *
     * @param message 消息数据
     */
    @Override
    public void onMessage(byte[] message) {
        if (message == null || message.length == 0) {
            logger.warn("接收到空的市场数据消息");
            return;
        }

        try {
            // 反序列化protobuf消息
            MarketDataMsg.MarketData marketData = protobufSerializer.deserialize(
                    message, MarketDataMsg.MarketData.parser());

            if (marketData == null) {
                logger.error("反序列化市场数据失败");
                return;
            }

            // 处理市场数据
            processMarketData(marketData);

            logger.debug("成功消费市场数据消息 - Symbol: {}, Timestamp: {}",
                    marketData.getSymbol(), marketData.getTimestamp());

        } catch (Exception e) {
            logger.error("消费市场数据消息失败: {}", e.getMessage(), e);
            // 抛出异常会触发重试机制
            throw new RuntimeException("消费市场数据失败", e);
        }
    }

    /**
     * 处理市场数据业务逻辑
     *
     * @param marketData 市场数据
     */
    private void processMarketData(MarketDataMsg.MarketData marketData) {
        // 这里可以添加具体的业务处理逻辑
        // 例如：
        // 1. 数据验证
        // 2. 存储到数据库
        // 3. 发送到其他系统
        // 4. 实时计算
        // 5. 风险控制等

        // 使用BigDecimal处理价格数据，确保精度
        BigDecimal bid = BigDecimalUtils.getBidFromMarketData(marketData);
        BigDecimal offer = BigDecimalUtils.getOfferFromMarketData(marketData);
        BigDecimal mid = BigDecimalUtils.getMidFromMarketData(marketData);

        logger.info("处理市场数据 - Symbol: {}, Bid: {}, Offer: {}, Mid: {}, DataCount: {}",
                marketData.getSymbol(),
                bid,
                offer,
                mid,
                marketData.getDataCount());

        // 处理深度数据 - 使用BigDecimal确保精度
        if (marketData.getDataCount() > 0) {
            marketData.getDataList().forEach(depthData -> {
                BigDecimal price = BigDecimalUtils.getPriceFromDepthData(depthData);
                BigDecimal size = BigDecimalUtils.getSizeFromDepthData(depthData);

                logger.debug("深度数据 - Type: {}, Price: {}, Size: {}, Condition: {}, Origin: {}",
                        depthData.getTp(),
                        price,
                        size,
                        depthData.getCondition(),
                        depthData.getOri());

                // 这里可以进行基于BigDecimal的业务计算
                // 例如：计算总价值、风险评估等
                if (BigDecimalUtils.isValidPrice(price) && BigDecimalUtils.isValidSize(size)) {
                    BigDecimal totalValue = BigDecimalUtils.safeMultiply(price, size);
                    logger.debug("深度数据总价值: {}", totalValue);
                }
            });
        }

        // 可以在这里添加更多业务逻辑
        // 例如：
        // - 实时价格监控
        // - 价格变动通知
        // - 数据持久化
        // - 指标计算
        // - 风险管理等
    }

    /**
     * 验证市场数据的有效性
     *
     * @param marketData 市场数据
     * @return 是否有效
     */
    private boolean isValidMarketData(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            return false;
        }

        // 检查必要字段
        if (marketData.getSymbol() == null || marketData.getSymbol().trim().isEmpty()) {
            logger.warn("市场数据缺少symbol字段");
            return false;
        }

        if (marketData.getTimestamp() <= 0) {
            logger.warn("市场数据时间戳无效: {}", marketData.getTimestamp());
            return false;
        }

        // 检查价格数据
        try {
            BigDecimal bid = BigDecimalUtils.getBidFromMarketData(marketData);
            BigDecimal offer = BigDecimalUtils.getOfferFromMarketData(marketData);

            if (!BigDecimalUtils.isValidPrice(bid) || !BigDecimalUtils.isValidPrice(offer)) {
                logger.warn("市场数据价格无效 - Symbol: {}, Bid: {}, Offer: {}",
                        marketData.getSymbol(), bid, offer);
                return false;
            }

            // 检查bid <= offer
            if (bid.compareTo(offer) > 0) {
                logger.warn("市场数据价格异常：bid > offer - Symbol: {}, Bid: {}, Offer: {}",
                        marketData.getSymbol(), bid, offer);
                return false;
            }

        } catch (Exception e) {
            logger.warn("验证市场数据价格时发生异常 - Symbol: {}, Error: {}",
                    marketData.getSymbol(), e.getMessage());
            return false;
        }

        return true;
    }
}
