package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.message.MarketDataMessage;
import com.flopotech.bg.quote.oz.util.ProtobufSerializer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * 市场数据RocketMQ生产者服务
 * 负责将市场数据发送到RocketMQ Topic
 */
@Service
public class RocketMQMarketDataProducer {

    private static final Logger logger = LoggerFactory.getLogger(RocketMQMarketDataProducer.class);

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Autowired
    private ProtobufSerializer protobufSerializer;

    @Value("${rocketmq.topic.market-data}")
    private String marketDataTopic;

    @Value("${rocketmq.producer.timeout:3000}")
    private long timeout;

    /**
     * 同步发送市场数据到RocketMQ
     *
     * @param marketData 市场数据消息
     * @return 发送结果
     */
    public SendResult sendMarketData(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("尝试发送null市场数据");
            return null;
        }

        try {
            // 序列化protobuf消息
            byte[] serializedData = protobufSerializer.serialize(marketData);

            if (serializedData == null) {
                logger.error("序列化市场数据失败，symbol: {}", marketData.getSymbol());
                return null;
            }

            // 使用symbol作为消息key，确保同一合约的数据有序
            String messageKey = marketData.getSymbol();

            // 使用symbol作为tag，便于消费者过滤
            String tag = marketData.getSymbol();

            // 构建消息
            Message<byte[]> message = MessageBuilder
                    .withPayload(serializedData)
                    .setHeader("KEYS", messageKey)
                    .setHeader("TAGS", tag)
                    .build();

            // 同步发送消息 - 使用有序消息，按symbol分区
            SendResult result = rocketMQTemplate.syncSendOrderly(
                    marketDataTopic + ":" + tag,
                    message,
                    messageKey, // 使用symbol作为分区key
                    timeout);

            logger.debug("成功发送市场数据到RocketMQ - Symbol: {}, Topic: {}, MessageId: {}, QueueId: {}",
                    marketData.getSymbol(),
                    marketDataTopic,
                    result.getMsgId(),
                    result.getMessageQueue().getQueueId());

            return result;

        } catch (Exception e) {
            logger.error("发送市场数据到RocketMQ失败 - Symbol: {}, Error: {}",
                    marketData.getSymbol(), e.getMessage(), e);
            throw new RuntimeException("发送市场数据失败", e);
        }
    }

    /**
     * 异步发送市场数据到RocketMQ
     *
     * @param marketData 市场数据消息
     */
    public void sendMarketDataAsync(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            logger.warn("尝试异步发送null市场数据");
            return;
        }

        try {
            // 序列化protobuf消息
            byte[] serializedData = protobufSerializer.serialize(marketData);

            if (serializedData == null) {
                logger.error("序列化市场数据失败，symbol: {}", marketData.getSymbol());
                return;
            }

            // 使用symbol作为消息key
            String messageKey = marketData.getSymbol();
            String tag = marketData.getSymbol();

            // 构建消息
            Message<byte[]> message = MessageBuilder
                    .withPayload(serializedData)
                    .setHeader("KEYS", messageKey)
                    .setHeader("TAGS", tag)
                    .build();

            // 异步发送消息
            rocketMQTemplate.asyncSendOrderly(
                    marketDataTopic + ":" + tag,
                    message,
                    messageKey,
                    new org.apache.rocketmq.client.producer.SendCallback() {
                        @Override
                        public void onSuccess(SendResult sendResult) {
                            logger.debug("异步发送市场数据成功 - Symbol: {}, MessageId: {}, QueueId: {}",
                                    marketData.getSymbol(),
                                    sendResult.getMsgId(),
                                    sendResult.getMessageQueue().getQueueId());
                        }

                        @Override
                        public void onException(Throwable e) {
                            logger.error("异步发送市场数据失败 - Symbol: {}, Error: {}",
                                    marketData.getSymbol(), e.getMessage(), e);
                        }
                    },
                    timeout);

        } catch (Exception e) {
            logger.error("异步发送市场数据到RocketMQ失败 - Symbol: {}, Error: {}",
                    marketData.getSymbol(), e.getMessage(), e);
        }
    }

    /**
     * 发送延迟消息
     *
     * @param marketData 市场数据消息
     * @param delayLevel 延迟级别 (1-18)
     * @return 发送结果
     */
    public SendResult sendDelayedMarketData(MarketDataMsg.MarketData marketData, int delayLevel) {
        if (marketData == null) {
            logger.warn("尝试发送null延迟市场数据");
            return null;
        }

        try {
            byte[] serializedData = protobufSerializer.serialize(marketData);
            if (serializedData == null) {
                logger.error("序列化延迟市场数据失败，symbol: {}", marketData.getSymbol());
                return null;
            }

            String messageKey = marketData.getSymbol();
            String tag = marketData.getSymbol();

            Message<byte[]> message = MessageBuilder
                    .withPayload(serializedData)
                    .setHeader("KEYS", messageKey)
                    .setHeader("TAGS", tag)
                    .build();

            // 发送延迟消息
            SendResult result = rocketMQTemplate.syncSend(
                    marketDataTopic + ":" + tag,
                    message,
                    timeout,
                    delayLevel);

            logger.debug("成功发送延迟市场数据到RocketMQ - Symbol: {}, DelayLevel: {}, MessageId: {}",
                    marketData.getSymbol(), delayLevel, result.getMsgId());

            return result;

        } catch (Exception e) {
            logger.error("发送延迟市场数据到RocketMQ失败 - Symbol: {}, Error: {}",
                    marketData.getSymbol(), e.getMessage(), e);
            throw new RuntimeException("发送延迟市场数据失败", e);
        }
    }
}
