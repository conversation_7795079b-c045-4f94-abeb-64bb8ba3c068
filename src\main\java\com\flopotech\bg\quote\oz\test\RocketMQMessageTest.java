package com.flopotech.bg.quote.oz.test;

import com.flopotech.bg.quote.oz.service.MarketDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * RocketMQ消息发送测试工具
 * 测试RocketMQ消息发送功能和有序消息
 */
@Component
@Order(2) // 在RocketMQProtobufExample之后运行
public class RocketMQMessageTest implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(RocketMQMessageTest.class);

    @Autowired
    private MarketDataService marketDataService;

    // 测试用的symbol列表
    private static final List<String> TEST_SYMBOLS = Arrays.asList(
            "EURUSD", "GBPUSD", "USDJPY", "USDCHF", "AUDUSD", 
            "USDCAD", "NZDUSD", "EURGBP", "EURJPY", "GBPJPY"
    );

    @Override
    public void run(String... args) throws Exception {
        // 等待一段时间，确保之前的示例消息已经处理完成
        Thread.sleep(2000);
        
        logger.info("=== 开始RocketMQ消息发送测试 ===");

        // 测试1：发送多个symbol的消息
        testSymbolMessaging();

        // 等待消息发送完成
        Thread.sleep(3000);

        // 测试2：发送相同symbol的多条消息（测试有序消息）
        testOrderedMessaging();

        // 等待消息发送完成
        Thread.sleep(3000);

        logger.info("=== RocketMQ消息发送测试完成 ===");
    }

    /**
     * 测试多个symbol的消息发送
     */
    private void testSymbolMessaging() {
        logger.info("--- 测试多Symbol消息发送 ---");

        Random random = new Random();
        
        // 为每个symbol发送一条消息
        for (String symbol : TEST_SYMBOLS) {
            double bid = 1.0000 + random.nextDouble() * 0.5;
            double offer = bid + 0.0002 + random.nextDouble() * 0.0008;
            
            try {
                marketDataService.processAndSendMarketData(
                        symbol, bid, offer, System.currentTimeMillis());
                
                logger.debug("发送测试消息: Symbol={}, Bid={}, Offer={}", symbol, bid, offer);
                
                // 短暂延迟，避免消息发送过快
                Thread.sleep(100);
            } catch (Exception e) {
                logger.error("发送消息失败 - Symbol: {}, Error: {}", symbol, e.getMessage());
            }
        }
        
        logger.info("已发送 {} 个不同symbol的测试消息", TEST_SYMBOLS.size());
    }

    /**
     * 测试有序消息发送
     * 为相同symbol发送多条消息，验证RocketMQ的有序消息功能
     */
    private void testOrderedMessaging() {
        logger.info("--- 测试有序消息发送 ---");

        Random random = new Random();
        
        // 选择几个symbol，每个发送多条消息
        List<String> testSymbols = Arrays.asList("EURUSD", "GBPUSD", "USDJPY");
        
        for (String symbol : testSymbols) {
            logger.debug("为Symbol {} 发送多条有序消息", symbol);
            
            // 为每个symbol发送5条消息
            for (int i = 0; i < 5; i++) {
                double bid = 1.0000 + random.nextDouble() * 0.5;
                double offer = bid + 0.0002 + random.nextDouble() * 0.0008;
                
                try {
                    marketDataService.processAndSendMarketData(
                            symbol, bid, offer, System.currentTimeMillis());
                    
                    logger.debug("发送有序消息 {}/5: Symbol={}, Bid={}, Offer={}", 
                            i + 1, symbol, bid, offer);
                    
                    // 短暂延迟，模拟实际市场数据间隔
                    Thread.sleep(50);
                } catch (Exception e) {
                    logger.error("发送有序消息失败 - Symbol: {}, Sequence: {}, Error: {}", 
                            symbol, i + 1, e.getMessage());
                }
            }
        }
        
        logger.info("有序消息测试完成，每个测试symbol发送了5条消息");
    }

    /**
     * 测试深度数据消息发送
     */
    public void testDepthDataMessaging() {
        logger.info("--- 测试深度数据消息发送 ---");

        // 创建买方深度数据
        List<MarketDataService.DepthInfo> bidDepths = Arrays.asList(
                new MarketDataService.DepthInfo(1.0850, 1000000, "A", "LP1", "bid-001"),
                new MarketDataService.DepthInfo(1.0849, 2000000, "A", "LP2", "bid-002"),
                new MarketDataService.DepthInfo(1.0848, 1500000, "I", "LP3", "bid-003")
        );

        // 创建卖方深度数据
        List<MarketDataService.DepthInfo> offerDepths = Arrays.asList(
                new MarketDataService.DepthInfo(1.0852, 1000000, "A", "LP1", "offer-001"),
                new MarketDataService.DepthInfo(1.0853, 2000000, "A", "LP2", "offer-002"),
                new MarketDataService.DepthInfo(1.0854, 1500000, "I", "LP3", "offer-003")
        );

        try {
            // 发送包含深度数据的市场数据
            marketDataService.processAndSendMarketDataWithDepth(
                    "EURUSD",
                    1.0850, // 最优bid
                    1.0852, // 最优offer
                    System.currentTimeMillis(),
                    bidDepths,
                    offerDepths
            );

            logger.info("成功发送深度数据消息");
        } catch (Exception e) {
            logger.error("发送深度数据消息失败: {}", e.getMessage());
        }
    }

    /**
     * 性能测试 - 批量发送消息
     */
    public void performanceTest() {
        logger.info("--- RocketMQ性能测试 ---");

        int messageCount = 100;
        long startTime = System.currentTimeMillis();
        Random random = new Random();

        for (int i = 0; i < messageCount; i++) {
            String symbol = TEST_SYMBOLS.get(i % TEST_SYMBOLS.size());
            double bid = 1.0000 + random.nextDouble() * 0.5;
            double offer = bid + 0.0002 + random.nextDouble() * 0.0008;

            try {
                marketDataService.processAndSendMarketData(
                        symbol, bid, offer, System.currentTimeMillis());
            } catch (Exception e) {
                logger.error("性能测试消息发送失败 - Message: {}, Error: {}", i + 1, e.getMessage());
            }
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        double throughput = (double) messageCount / duration * 1000;

        logger.info("性能测试完成 - 发送 {} 条消息，耗时 {} ms，吞吐量: {:.2f} msg/s", 
                messageCount, duration, throughput);
    }
}
