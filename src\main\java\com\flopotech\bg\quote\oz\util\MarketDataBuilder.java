package com.flopotech.bg.quote.oz.util;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 市场数据构建器工具类
 * 用于方便地创建MarketData protobuf消息
 */
@Component
public class MarketDataBuilder {

    /**
     * 创建市场数据消息
     *
     * @param timestamp 时间戳
     * @param symbol    合约
     * @param bid       最优bid价
     * @param offer     最优offer价
     * @return MarketData消息
     */
    public MarketDataMsg.MarketData createMarketData(
            long timestamp, String symbol, BigDecimal bid, BigDecimal offer) {

        BigDecimal mid = BigDecimalUtils.calculateMid(bid, offer);

        return MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(timestamp)
                .setSymbol(symbol)
                .setBid(BigDecimalUtils.toString(bid))
                .setOffer(BigDecimalUtils.toString(offer))
                .setMid(BigDecimalUtils.toString(mid))
                .build();
    }

    /**
     * 创建市场数据消息 - 兼容double参数的重载方法
     *
     * @param timestamp 时间戳
     * @param symbol    合约
     * @param bid       最优bid价
     * @param offer     最优offer价
     * @return MarketData消息
     */
    public MarketDataMsg.MarketData createMarketData(
            long timestamp, String symbol, double bid, double offer) {
        return createMarketData(timestamp, symbol,
                BigDecimalUtils.fromDouble(bid),
                BigDecimalUtils.fromDouble(offer));
    }

    /**
     * 创建包含深度数据的市场数据消息
     *
     * @param timestamp     时间戳
     * @param symbol        合约
     * @param bid           最优bid价
     * @param offer         最优offer价
     * @param depthDataList 深度数据列表
     * @return MarketData消息
     */
    public MarketDataMsg.MarketData createMarketDataWithDepth(
            long timestamp, String symbol, BigDecimal bid, BigDecimal offer,
            List<MarketDataMsg.DepthData> depthDataList) {

        BigDecimal mid = BigDecimalUtils.calculateMid(bid, offer);

        MarketDataMsg.MarketData.Builder builder = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(timestamp)
                .setSymbol(symbol)
                .setBid(BigDecimalUtils.toString(bid))
                .setOffer(BigDecimalUtils.toString(offer))
                .setMid(BigDecimalUtils.toString(mid));

        if (depthDataList != null && !depthDataList.isEmpty()) {
            builder.addAllData(depthDataList);
        }

        return builder.build();
    }

    /**
     * 创建包含深度数据的市场数据消息 - 兼容double参数的重载方法
     *
     * @param timestamp     时间戳
     * @param symbol        合约
     * @param bid           最优bid价
     * @param offer         最优offer价
     * @param depthDataList 深度数据列表
     * @return MarketData消息
     */
    public MarketDataMsg.MarketData createMarketDataWithDepth(
            long timestamp, String symbol, double bid, double offer,
            List<MarketDataMsg.DepthData> depthDataList) {
        return createMarketDataWithDepth(timestamp, symbol,
                BigDecimalUtils.fromDouble(bid),
                BigDecimalUtils.fromDouble(offer),
                depthDataList);
    }

    /**
     * 创建深度数据
     *
     * @param tp        价格类型 (0-bid, 1-offer)
     * @param px        价格
     * @param sz        可交易量
     * @param condition 报价条件
     * @param ori       发起者
     * @param uqId      报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createDepthData(
            int tp, BigDecimal px, BigDecimal sz, String condition, String ori, String uqId) {

        return MarketDataMsg.DepthData.newBuilder()
                .setTp(tp)
                .setPx(BigDecimalUtils.toString(px))
                .setSz(BigDecimalUtils.toString(sz))
                .setCondition(condition != null ? condition : "")
                .setOri(ori != null ? ori : "")
                .setUqId(uqId != null ? uqId : "")
                .build();
    }

    /**
     * 创建深度数据 - 兼容double参数的重载方法
     *
     * @param tp        价格类型 (0-bid, 1-offer)
     * @param px        价格
     * @param sz        可交易量
     * @param condition 报价条件
     * @param ori       发起者
     * @param uqId      报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createDepthData(
            int tp, double px, double sz, String condition, String ori, String uqId) {
        return createDepthData(tp,
                BigDecimalUtils.fromDouble(px),
                BigDecimalUtils.fromDouble(sz),
                condition, ori, uqId);
    }

    /**
     * 创建可交易报价深度数据
     *
     * @param tp   价格类型 (0-bid, 1-offer)
     * @param px   价格
     * @param sz   可交易量
     * @param ori  发起者
     * @param uqId 报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createTradeableDepthData(
            int tp, BigDecimal px, BigDecimal sz, String ori, String uqId) {
        return createDepthData(tp, px, sz, "A", ori, uqId);
    }

    /**
     * 创建可交易报价深度数据 - 兼容double参数的重载方法
     *
     * @param tp   价格类型 (0-bid, 1-offer)
     * @param px   价格
     * @param sz   可交易量
     * @param ori  发起者
     * @param uqId 报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createTradeableDepthData(
            int tp, double px, double sz, String ori, String uqId) {
        return createDepthData(tp, px, sz, "A", ori, uqId);
    }

    /**
     * 创建参考报价深度数据
     *
     * @param tp   价格类型 (0-bid, 1-offer)
     * @param px   价格
     * @param sz   可交易量
     * @param ori  发起者
     * @param uqId 报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createIndicativeDepthData(
            int tp, BigDecimal px, BigDecimal sz, String ori, String uqId) {
        return createDepthData(tp, px, sz, "I", ori, uqId);
    }

    /**
     * 创建参考报价深度数据 - 兼容double参数的重载方法
     *
     * @param tp   价格类型 (0-bid, 1-offer)
     * @param px   价格
     * @param sz   可交易量
     * @param ori  发起者
     * @param uqId 报价单唯一标识
     * @return DepthData消息
     */
    public MarketDataMsg.DepthData createIndicativeDepthData(
            int tp, double px, double sz, String ori, String uqId) {
        return createDepthData(tp, px, sz, "I", ori, uqId);
    }

    /**
     * 从现有MarketData创建新的MarketData（用于更新）
     *
     * @param original 原始MarketData
     * @param newBid   新的bid价
     * @param newOffer 新的offer价
     * @return 更新后的MarketData消息
     */
    public MarketDataMsg.MarketData updateMarketData(
            MarketDataMsg.MarketData original, BigDecimal newBid, BigDecimal newOffer) {

        BigDecimal newMid = BigDecimalUtils.calculateMid(newBid, newOffer);

        return MarketDataMsg.MarketData.newBuilder(original)
                .setBid(BigDecimalUtils.toString(newBid))
                .setOffer(BigDecimalUtils.toString(newOffer))
                .setMid(BigDecimalUtils.toString(newMid))
                .setTimestamp(System.currentTimeMillis())
                .build();
    }

    /**
     * 从现有MarketData创建新的MarketData（用于更新）- 兼容double参数的重载方法
     *
     * @param original 原始MarketData
     * @param newBid   新的bid价
     * @param newOffer 新的offer价
     * @return 更新后的MarketData消息
     */
    public MarketDataMsg.MarketData updateMarketData(
            MarketDataMsg.MarketData original, double newBid, double newOffer) {
        return updateMarketData(original,
                BigDecimalUtils.fromDouble(newBid),
                BigDecimalUtils.fromDouble(newOffer));
    }

    /**
     * 验证MarketData消息的有效性
     *
     * @param marketData MarketData消息
     * @return 是否有效
     */
    public boolean isValidMarketData(MarketDataMsg.MarketData marketData) {
        if (marketData == null) {
            return false;
        }

        // 检查必要字段
        if (marketData.getSymbol() == null || marketData.getSymbol().trim().isEmpty()) {
            return false;
        }

        if (marketData.getTimestamp() <= 0) {
            return false;
        }

        // 检查价格的合理性
        BigDecimal bid = BigDecimalUtils.fromString(marketData.getBid());
        BigDecimal offer = BigDecimalUtils.fromString(marketData.getOffer());

        if (!BigDecimalUtils.isValidPrice(bid) || !BigDecimalUtils.isValidPrice(offer)) {
            return false;
        }

        if (bid.compareTo(offer) >= 0) {
            return false;
        }

        return true;
    }
}
