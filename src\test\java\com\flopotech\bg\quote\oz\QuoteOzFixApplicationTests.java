package com.flopotech.bg.quote.oz;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest(properties = {
		"quickfix.onezero.auto-start=false",
		"rocketmq.name-server=localhost:9876"
})
@ActiveProfiles("test")
class QuoteOzFixApplicationTests {

	@Test
	void contextLoads() {
		// 简单的上下文加载测试
		// 在测试环境中，RocketMQ消费者不会启动
	}

}
