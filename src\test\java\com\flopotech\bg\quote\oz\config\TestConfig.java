package com.flopotech.bg.quote.oz.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;

/**
 * 测试配置类
 * 用于在测试环境中提供mock bean
 */
@TestConfiguration
public class TestConfig {

    /**
     * 在测试环境中提供一个空的RocketMQ消费者实现
     * 避免在没有RocketMQ服务器的情况下启动失败
     */
    @Bean
    @Primary
    @Profile("test")
    public Object mockRocketMQConsumer() {
        // 返回一个空的对象，避免RocketMQ消费者启动
        return new Object();
    }
}
