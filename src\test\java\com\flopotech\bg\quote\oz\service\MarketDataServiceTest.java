package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.MarketDataBuilder;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageQueue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 市场数据服务测试类
 */
@ExtendWith(MockitoExtension.class)
class MarketDataServiceTest {

    @Mock
    private RocketMQMarketDataProducer rocketMQMarketDataProducer;

    @Mock
    private MarketDataBuilder marketDataBuilder;

    @InjectMocks
    private MarketDataService marketDataService;

    private MarketDataMsg.MarketData testMarketData;
    private SendResult mockSendResult;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testMarketData = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSymbol("EURUSD")
                .setBid("1.0850")
                .setOffer("1.0852")
                .setMid("1.0851")
                .build();

        // 创建mock发送结果
        mockSendResult = mock(SendResult.class);
        MessageQueue mockMessageQueue = mock(MessageQueue.class);
        when(mockSendResult.getMsgId()).thenReturn("test-msg-id");
        when(mockSendResult.getMessageQueue()).thenReturn(mockMessageQueue);
        when(mockMessageQueue.getQueueId()).thenReturn(1);
    }

    @Test
    void testProcessAndSendMarketData_BigDecimal_Success() {
        // 准备测试数据
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        long timestamp = System.currentTimeMillis();

        // 配置mock行为
        when(marketDataBuilder.createMarketData(timestamp, "EURUSD", bid, offer))
                .thenReturn(testMarketData);
        when(marketDataBuilder.isValidMarketData(testMarketData)).thenReturn(true);
        when(rocketMQMarketDataProducer.sendMarketData(testMarketData)).thenReturn(mockSendResult);

        // 执行测试
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketData("EURUSD", bid, offer, timestamp);
        });

        // 验证方法调用
        verify(marketDataBuilder).createMarketData(timestamp, "EURUSD", bid, offer);
        verify(marketDataBuilder).isValidMarketData(testMarketData);
        verify(rocketMQMarketDataProducer).sendMarketData(testMarketData);
    }

    @Test
    void testProcessAndSendMarketData_Double_Success() {
        // 准备测试数据
        double bid = 1.0850;
        double offer = 1.0852;
        long timestamp = System.currentTimeMillis();

        // 配置mock行为
        when(marketDataBuilder.createMarketData(eq(timestamp), eq("EURUSD"), any(BigDecimal.class),
                any(BigDecimal.class)))
                .thenReturn(testMarketData);
        when(marketDataBuilder.isValidMarketData(testMarketData)).thenReturn(true);
        when(rocketMQMarketDataProducer.sendMarketData(testMarketData)).thenReturn(mockSendResult);

        // 执行测试
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketData("EURUSD", bid, offer, timestamp);
        });

        // 验证方法调用
        verify(marketDataBuilder).createMarketData(eq(timestamp), eq("EURUSD"), any(BigDecimal.class),
                any(BigDecimal.class));
        verify(marketDataBuilder).isValidMarketData(testMarketData);
        verify(rocketMQMarketDataProducer).sendMarketData(testMarketData);
    }

    @Test
    void testProcessAndSendMarketData_InvalidData() {
        // 准备测试数据
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        long timestamp = System.currentTimeMillis();

        // 配置mock行为 - 数据验证失败
        when(marketDataBuilder.createMarketData(timestamp, "EURUSD", bid, offer))
                .thenReturn(testMarketData);
        when(marketDataBuilder.isValidMarketData(testMarketData)).thenReturn(false);

        // 执行测试
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketData("EURUSD", bid, offer, timestamp);
        });

        // 验证方法调用
        verify(marketDataBuilder).createMarketData(timestamp, "EURUSD", bid, offer);
        verify(marketDataBuilder).isValidMarketData(testMarketData);
        verify(rocketMQMarketDataProducer, never()).sendMarketData(any());
    }

    @Test
    void testProcessAndSendMarketData_SendFailure() {
        // 准备测试数据
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        long timestamp = System.currentTimeMillis();

        // 配置mock行为 - 发送失败
        when(marketDataBuilder.createMarketData(timestamp, "EURUSD", bid, offer))
                .thenReturn(testMarketData);
        when(marketDataBuilder.isValidMarketData(testMarketData)).thenReturn(true);
        when(rocketMQMarketDataProducer.sendMarketData(testMarketData)).thenReturn(null);

        // 执行测试
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketData("EURUSD", bid, offer, timestamp);
        });

        // 验证方法调用
        verify(marketDataBuilder).createMarketData(timestamp, "EURUSD", bid, offer);
        verify(marketDataBuilder).isValidMarketData(testMarketData);
        verify(rocketMQMarketDataProducer).sendMarketData(testMarketData);
    }

    @Test
    void testProcessAndSendMarketDataWithDepth_Success() {
        // 准备测试数据
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        long timestamp = System.currentTimeMillis();

        List<MarketDataService.DepthInfo> bidDepths = Arrays.asList(
                new MarketDataService.DepthInfo(1.0850, 1000000, "A", "LP1", "bid-001"));

        List<MarketDataService.DepthInfo> offerDepths = Arrays.asList(
                new MarketDataService.DepthInfo(1.0852, 1000000, "A", "LP1", "offer-001"));

        MarketDataMsg.DepthData mockDepthData = MarketDataMsg.DepthData.newBuilder()
                .setTp(0)
                .setPx("1.0850")
                .setSz("1000000")
                .setCondition("A")
                .setOri("LP1")
                .setUqId("bid-001")
                .build();

        MarketDataMsg.MarketData marketDataWithDepth = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(timestamp)
                .setSymbol("EURUSD")
                .setBid("1.0850")
                .setOffer("1.0852")
                .setMid("1.0851")
                .addData(mockDepthData)
                .build();

        // 配置mock行为
        when(marketDataBuilder.createDepthData(anyInt(), any(BigDecimal.class), any(BigDecimal.class),
                anyString(), anyString(), anyString())).thenReturn(mockDepthData);
        when(marketDataBuilder.createMarketDataWithDepth(eq(timestamp), eq("EURUSD"), eq(bid), eq(offer), anyList()))
                .thenReturn(marketDataWithDepth);
        when(marketDataBuilder.isValidMarketData(marketDataWithDepth)).thenReturn(true);
        when(rocketMQMarketDataProducer.sendMarketData(marketDataWithDepth)).thenReturn(mockSendResult);

        // 执行测试
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketDataWithDepth("EURUSD", bid, offer, timestamp, bidDepths,
                    offerDepths);
        });

        // 验证方法调用
        verify(marketDataBuilder, times(2)).createDepthData(anyInt(), any(BigDecimal.class), any(BigDecimal.class),
                anyString(), anyString(), anyString());
        verify(marketDataBuilder).createMarketDataWithDepth(eq(timestamp), eq("EURUSD"), eq(bid), eq(offer), anyList());
        verify(marketDataBuilder).isValidMarketData(marketDataWithDepth);
        verify(rocketMQMarketDataProducer).sendMarketData(marketDataWithDepth);
    }

    @Test
    void testProcessAndSendMarketDataWithDepth_EmptyDepths() {
        // 准备测试数据
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        long timestamp = System.currentTimeMillis();

        // 配置mock行为
        when(marketDataBuilder.createMarketDataWithDepth(eq(timestamp), eq("EURUSD"), eq(bid), eq(offer), anyList()))
                .thenReturn(testMarketData);
        when(marketDataBuilder.isValidMarketData(testMarketData)).thenReturn(true);
        when(rocketMQMarketDataProducer.sendMarketData(testMarketData)).thenReturn(mockSendResult);

        // 执行测试 - 传入null深度数据
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketDataWithDepth("EURUSD", bid, offer, timestamp, null, null);
        });

        // 验证方法调用
        verify(marketDataBuilder, never()).createDepthData(anyInt(), any(BigDecimal.class), any(BigDecimal.class),
                anyString(), anyString(), anyString());
        verify(marketDataBuilder).createMarketDataWithDepth(eq(timestamp), eq("EURUSD"), eq(bid), eq(offer), anyList());
        verify(marketDataBuilder).isValidMarketData(testMarketData);
        verify(rocketMQMarketDataProducer).sendMarketData(testMarketData);
    }

    @Test
    void testProcessAndSendMarketDataWithDepth_Exception() {
        // 准备测试数据
        BigDecimal bid = new BigDecimal("1.0850");
        BigDecimal offer = new BigDecimal("1.0852");
        long timestamp = System.currentTimeMillis();

        // 配置mock行为 - 抛出异常
        when(marketDataBuilder.createMarketDataWithDepth(anyLong(), anyString(), any(BigDecimal.class),
                any(BigDecimal.class), anyList()))
                .thenThrow(new RuntimeException("创建市场数据失败"));

        // 执行测试 - 应该捕获异常而不抛出
        assertDoesNotThrow(() -> {
            marketDataService.processAndSendMarketDataWithDepth("EURUSD", bid, offer, timestamp, null, null);
        });

        // 验证方法调用
        verify(marketDataBuilder).createMarketDataWithDepth(anyLong(), anyString(), any(BigDecimal.class),
                any(BigDecimal.class), anyList());
        verify(rocketMQMarketDataProducer, never()).sendMarketData(any());
    }
}
