package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.ProtobufSerializer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RocketMQ市场数据消费者测试类
 */
@ExtendWith(MockitoExtension.class)
class RocketMQMarketDataConsumerTest {

    @Mock
    private ProtobufSerializer protobufSerializer;

    @InjectMocks
    private RocketMQMarketDataConsumer consumer;

    private MarketDataMsg.MarketData testMarketData;
    private byte[] testMessageData;

    @BeforeEach
    void setUp() {
        // 创建测试数据
        testMarketData = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSymbol("EURUSD")
                .setBid("1.0850")
                .setOffer("1.0852")
                .setMid("1.0851")
                .build();

        testMessageData = new byte[]{1, 2, 3, 4, 5}; // 模拟消息数据
    }

    @Test
    void testOnMessage_Success() {
        // 配置mock行为
        when(protobufSerializer.deserialize(testMessageData, MarketDataMsg.MarketData.parser()))
                .thenReturn(testMarketData);

        // 执行测试
        assertDoesNotThrow(() -> {
            consumer.onMessage(testMessageData);
        });

        // 验证方法调用
        verify(protobufSerializer).deserialize(testMessageData, MarketDataMsg.MarketData.parser());
    }

    @Test
    void testOnMessage_NullMessage() {
        // 执行测试
        assertDoesNotThrow(() -> {
            consumer.onMessage(null);
        });

        // 验证没有调用反序列化方法
        verify(protobufSerializer, never()).deserialize(any(), any());
    }

    @Test
    void testOnMessage_EmptyMessage() {
        // 执行测试
        assertDoesNotThrow(() -> {
            consumer.onMessage(new byte[0]);
        });

        // 验证没有调用反序列化方法
        verify(protobufSerializer, never()).deserialize(any(), any());
    }

    @Test
    void testOnMessage_DeserializationFailure() {
        // 配置mock行为 - 反序列化失败
        when(protobufSerializer.deserialize(testMessageData, MarketDataMsg.MarketData.parser()))
                .thenReturn(null);

        // 执行测试
        assertDoesNotThrow(() -> {
            consumer.onMessage(testMessageData);
        });

        // 验证方法调用
        verify(protobufSerializer).deserialize(testMessageData, MarketDataMsg.MarketData.parser());
    }

    @Test
    void testOnMessage_ProcessingException() {
        // 配置mock行为 - 反序列化抛出异常
        when(protobufSerializer.deserialize(testMessageData, MarketDataMsg.MarketData.parser()))
                .thenThrow(new RuntimeException("反序列化失败"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            consumer.onMessage(testMessageData);
        });

        // 验证方法调用
        verify(protobufSerializer).deserialize(testMessageData, MarketDataMsg.MarketData.parser());
    }

    @Test
    void testOnMessage_WithDepthData() {
        // 创建包含深度数据的测试数据
        MarketDataMsg.DepthData depthData = MarketDataMsg.DepthData.newBuilder()
                .setTp(0) // bid
                .setPx("1.0850")
                .setSz("1000000")
                .setCondition("A")
                .setOri("LP1")
                .setUqId("bid-001")
                .build();

        MarketDataMsg.MarketData marketDataWithDepth = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSymbol("EURUSD")
                .setBid("1.0850")
                .setOffer("1.0852")
                .setMid("1.0851")
                .addData(depthData)
                .build();

        // 配置mock行为
        when(protobufSerializer.deserialize(testMessageData, MarketDataMsg.MarketData.parser()))
                .thenReturn(marketDataWithDepth);

        // 执行测试
        assertDoesNotThrow(() -> {
            consumer.onMessage(testMessageData);
        });

        // 验证方法调用
        verify(protobufSerializer).deserialize(testMessageData, MarketDataMsg.MarketData.parser());
    }

    @Test
    void testOnMessage_MultipleDepthData() {
        // 创建包含多个深度数据的测试数据
        MarketDataMsg.DepthData bidDepth = MarketDataMsg.DepthData.newBuilder()
                .setTp(0) // bid
                .setPx("1.0850")
                .setSz("1000000")
                .setCondition("A")
                .setOri("LP1")
                .setUqId("bid-001")
                .build();

        MarketDataMsg.DepthData offerDepth = MarketDataMsg.DepthData.newBuilder()
                .setTp(1) // offer
                .setPx("1.0852")
                .setSz("2000000")
                .setCondition("A")
                .setOri("LP2")
                .setUqId("offer-001")
                .build();

        MarketDataMsg.MarketData marketDataWithMultipleDepth = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSymbol("GBPUSD")
                .setBid("1.2650")
                .setOffer("1.2653")
                .setMid("1.2651")
                .addData(bidDepth)
                .addData(offerDepth)
                .build();

        // 配置mock行为
        when(protobufSerializer.deserialize(testMessageData, MarketDataMsg.MarketData.parser()))
                .thenReturn(marketDataWithMultipleDepth);

        // 执行测试
        assertDoesNotThrow(() -> {
            consumer.onMessage(testMessageData);
        });

        // 验证方法调用
        verify(protobufSerializer).deserialize(testMessageData, MarketDataMsg.MarketData.parser());
    }

    @Test
    void testOnMessage_InvalidMarketData() {
        // 创建无效的市场数据（缺少必要字段）
        MarketDataMsg.MarketData invalidMarketData = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(0) // 无效时间戳
                .setSymbol("") // 空symbol
                .setBid("invalid") // 无效价格
                .setOffer("invalid") // 无效价格
                .build();

        // 配置mock行为
        when(protobufSerializer.deserialize(testMessageData, MarketDataMsg.MarketData.parser()))
                .thenReturn(invalidMarketData);

        // 执行测试 - 应该能处理无效数据而不抛出异常
        assertDoesNotThrow(() -> {
            consumer.onMessage(testMessageData);
        });

        // 验证方法调用
        verify(protobufSerializer).deserialize(testMessageData, MarketDataMsg.MarketData.parser());
    }
}
