package com.flopotech.bg.quote.oz.service;

import com.flopotech.bg.quote.oz.msg.MarketDataMsg;
import com.flopotech.bg.quote.oz.util.MarketDataBuilder;
import com.flopotech.bg.quote.oz.util.ProtobufSerializer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageQueue;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * RocketMQ市场数据生产者测试类
 */
@ExtendWith(MockitoExtension.class)
class RocketMQMarketDataProducerTest {

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    @Mock
    private ProtobufSerializer protobufSerializer;

    @InjectMocks
    private RocketMQMarketDataProducer producer;

    @Mock
    private MarketDataBuilder marketDataBuilder;

    private MarketDataMsg.MarketData testMarketData;
    private byte[] testSerializedData;

    @BeforeEach
    void setUp() {
        // 设置测试配置
        ReflectionTestUtils.setField(producer, "marketDataTopic", "test-market-data-topic");
        ReflectionTestUtils.setField(producer, "timeout", 3000L);

        // 创建测试数据
        testMarketData = MarketDataMsg.MarketData.newBuilder()
                .setTimestamp(System.currentTimeMillis())
                .setSymbol("EURUSD")
                .setBid("1.0850")
                .setOffer("1.0852")
                .setMid("1.0851")
                .build();

        testSerializedData = new byte[] { 1, 2, 3, 4, 5 }; // 模拟序列化数据
    }

    @Test
    void testSendMarketData_Success() {
        // 准备测试数据
        SendResult mockSendResult = mock(SendResult.class);
        MessageQueue mockMessageQueue = mock(MessageQueue.class);
        when(mockSendResult.getMsgId()).thenReturn("test-msg-id");
        when(mockSendResult.getMessageQueue()).thenReturn(mockMessageQueue);
        when(mockMessageQueue.getQueueId()).thenReturn(1);

        // 配置mock行为
        when(protobufSerializer.serialize(testMarketData)).thenReturn(testSerializedData);
        when(rocketMQTemplate.syncSendOrderly(anyString(), any(org.springframework.messaging.Message.class),
                anyString(), anyLong()))
                .thenReturn(mockSendResult);

        // 执行测试
        SendResult result = producer.sendMarketData(testMarketData);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-msg-id", result.getMsgId());

        // 验证方法调用
        verify(protobufSerializer).serialize(testMarketData);
        verify(rocketMQTemplate).syncSendOrderly(
                eq("test-market-data-topic:EURUSD"),
                any(org.springframework.messaging.Message.class),
                eq("EURUSD"),
                eq(3000L));
    }

    @Test
    void testSendMarketData_NullInput() {
        // 执行测试
        SendResult result = producer.sendMarketData(null);

        // 验证结果
        assertNull(result);

        // 验证没有调用其他方法
        verify(protobufSerializer, never()).serialize(any());
        verify(rocketMQTemplate, never()).syncSendOrderly(anyString(), any(org.springframework.messaging.Message.class),
                anyString(), anyLong());
    }

    @Test
    void testSendMarketData_SerializationFailure() {
        // 配置mock行为 - 序列化失败
        when(protobufSerializer.serialize(testMarketData)).thenReturn(null);

        // 执行测试
        SendResult result = producer.sendMarketData(testMarketData);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(protobufSerializer).serialize(testMarketData);
        verify(rocketMQTemplate, never()).syncSendOrderly(anyString(), any(org.springframework.messaging.Message.class),
                anyString(), anyLong());
    }

    @Test
    void testSendMarketData_RocketMQException() {
        // 配置mock行为
        when(protobufSerializer.serialize(testMarketData)).thenReturn(testSerializedData);
        when(rocketMQTemplate.syncSendOrderly(anyString(), any(org.springframework.messaging.Message.class),
                anyString(), anyLong()))
                .thenThrow(new RuntimeException("RocketMQ发送失败"));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            producer.sendMarketData(testMarketData);
        });

        // 验证方法调用
        verify(protobufSerializer).serialize(testMarketData);
        verify(rocketMQTemplate).syncSendOrderly(anyString(), any(org.springframework.messaging.Message.class),
                anyString(), anyLong());
    }

    @Test
    void testSendMarketDataAsync_Success() {
        // 配置mock行为
        when(protobufSerializer.serialize(testMarketData)).thenReturn(testSerializedData);

        // 执行测试
        assertDoesNotThrow(() -> {
            producer.sendMarketDataAsync(testMarketData);
        });

        // 验证方法调用
        verify(protobufSerializer).serialize(testMarketData);
        verify(rocketMQTemplate).asyncSendOrderly(
                eq("test-market-data-topic:EURUSD"),
                any(org.springframework.messaging.Message.class),
                eq("EURUSD"),
                any(),
                eq(3000L));
    }

    @Test
    void testSendMarketDataAsync_NullInput() {
        // 执行测试
        assertDoesNotThrow(() -> {
            producer.sendMarketDataAsync(null);
        });

        // 验证没有调用其他方法
        verify(protobufSerializer, never()).serialize(any());
        verify(rocketMQTemplate, never()).asyncSendOrderly(anyString(), any(), anyString(), any(), anyLong());
    }

    @Test
    void testSendDelayedMarketData_Success() {
        // 准备测试数据
        SendResult mockSendResult = mock(SendResult.class);
        MessageQueue mockMessageQueue = mock(MessageQueue.class);
        when(mockSendResult.getMsgId()).thenReturn("test-delayed-msg-id");
        when(mockSendResult.getMessageQueue()).thenReturn(mockMessageQueue);
        when(mockMessageQueue.getQueueId()).thenReturn(1);
        int delayLevel = 3;

        // 配置mock行为
        when(protobufSerializer.serialize(testMarketData)).thenReturn(testSerializedData);
        when(rocketMQTemplate.syncSend(anyString(), any(org.springframework.messaging.Message.class), anyLong(),
                anyInt()))
                .thenReturn(mockSendResult);

        // 执行测试
        SendResult result = producer.sendDelayedMarketData(testMarketData, delayLevel);

        // 验证结果
        assertNotNull(result);
        assertEquals("test-delayed-msg-id", result.getMsgId());

        // 验证方法调用
        verify(protobufSerializer).serialize(testMarketData);
        verify(rocketMQTemplate).syncSend(
                eq("test-market-data-topic:EURUSD"),
                any(org.springframework.messaging.Message.class),
                eq(3000L),
                eq(delayLevel));
    }

    @Test
    void testSendDelayedMarketData_NullInput() {
        // 执行测试
        SendResult result = producer.sendDelayedMarketData(null, 3);

        // 验证结果
        assertNull(result);

        // 验证没有调用其他方法
        verify(protobufSerializer, never()).serialize(any());
        verify(rocketMQTemplate, never()).syncSend(anyString(), any(), anyLong(), anyInt());
    }
}
