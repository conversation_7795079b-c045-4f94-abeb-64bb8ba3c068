# 测试环境配置
spring.application.name=bg-oz-quote-test

# 禁用QuickFIX自动启动
quickfix.onezero.auto-start=false

# RocketMQ测试配置 - 使用mock配置
rocketmq.name-server=localhost:9876
rocketmq.producer.group=test-producer-group
rocketmq.consumer.group=test-consumer-group
rocketmq.topic.market-data=test-market-data-topic

# 禁用RocketMQ自动配置
spring.autoconfigure.exclude=org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration

# 日志配置
logging.level.com.flopotech=DEBUG
logging.level.org.apache.rocketmq=WARN
logging.level.org.springframework=WARN
